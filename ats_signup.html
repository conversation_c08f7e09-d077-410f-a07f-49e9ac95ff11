<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Start Using Canvider ATS Today | Sign Up</title>
    <!-- Font Awesome for icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Modern Design System CSS -->
    <link rel="stylesheet" href="style.css" />
    <!-- favicon -->
    <link rel="icon" type="image/x-icon" href="./Static/favicon.png" />
  </head>
  <body>
    <div class="bg-animation"></div>

    <header class="header">
      <nav class="navbar nav-container">
        <div class="logo">
          <a href="./index.html">
            <img src="./Static/canvider_white.png" alt="logo" height="32" />
          </a>
        </div>
        <div class="menu-toggle" onclick="toggleMenu()">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <div class="nav-content">
          <ul class="nav-links">
            <li><a href="./index.html#features">Features</a></li>
            <li><a href="./index.html#pricing">Pricing</a></li>
            <li><a href="./about_us.html">About</a></li>
            <li><a href="./index.html#contact">Contact</a></li>
          </ul>
          <div class="nav-buttons">
            <a href="./index.html" class="btn btn-secondary">Back to Home</a>
            
          </div>
        </div>
      </nav>
    </header>

    <script>
      function toggleMenu() {
        document.querySelector('.nav-content').classList.toggle('active');
      }
      
      window.addEventListener('scroll', () => {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
          header.classList.add('scrolled');
        } else {
          header.classList.remove('scrolled');
        }
      });
    </script>

    <!-- Hero Section -->
    <section class="hero" style="min-height: 60vh; padding-top: 8rem;">
      <div class="hero-content">
        <div style="display: inline-block; background: var(--primary-gradient); padding: 0.5rem 1rem; border-radius: 50px; color: white; font-size: 0.9rem; font-weight: 600; margin-bottom: 1.5rem;">
          🚀 Start Your ATS Journey
        </div>
        <h1 style="font-size: clamp(2.5rem, 6vw, 4rem);">Start Using Canvider ATS Today</h1>
        <p style="font-size: 1.2rem; max-width: 600px; margin: 0 auto 2rem;">
          Join hundreds of companies that trust Canvider to streamline their hiring process. 
          Get started in minutes and transform your recruitment workflow.
        </p>
      </div>
    </section>

    <!-- Signup Form Section -->
    <section style="padding: 4rem 5%; background: rgba(10, 10, 10, 0.5);">
      <div class="container" style="max-width: 800px;">
        <div style="background: var(--surface-dark); border: 1px solid var(--border-subtle); border-radius: 20px; padding: 3rem;">
          <h2 style="color: var(--text-primary); text-align: center; margin-bottom: 2rem;">Create Your Account</h2>
          
          <form id="ats-signup-form" style="display: grid; gap: 1.5rem;">
            <!-- Company Information -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
              <div class="form-group">
                <label for="companyName" style="color: var(--text-secondary); margin-bottom: 0.5rem; display: block;">Company Name *</label>
                <input
                  type="text"
                  id="companyName"
                  name="companyName"
                  placeholder="Your Company Name"
                  required
                  style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-subtle); border-radius: 10px; background: rgba(255, 255, 255, 0.05); color: var(--text-primary); font-size: 1rem;"
                />
              </div>
              
              <div class="form-group">
                <label for="industry" style="color: var(--text-secondary); margin-bottom: 0.5rem; display: block;">Industry *</label>
                <select
                  id="industry"
                  name="industry"
                  required
                  style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-subtle); border-radius: 10px; background: rgba(255, 255, 255, 0.05); color: var(--text-primary); font-size: 1rem;"
                >
                  <option value="">Select Industry</option>
                  <option value="IT">Information Technology</option>
                  <option value="Finance">Finance & Banking</option>
                  <option value="Healthcare">Healthcare</option>
                  <option value="Manufacturing">Manufacturing</option>
                  <option value="Retail">Retail & E-commerce</option>
                  <option value="Education">Education</option>
                  <option value="Consulting">Consulting</option>
                  <option value="Other">Other</option>
                </select>
              </div>
            </div>

            <!-- Contact Information -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
              <div class="form-group">
                <label for="contactName" style="color: var(--text-secondary); margin-bottom: 0.5rem; display: block;">Contact Person *</label>
                <input
                  type="text"
                  id="contactName"
                  name="contactName"
                  placeholder="Your Full Name"
                  required
                  style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-subtle); border-radius: 10px; background: rgba(255, 255, 255, 0.05); color: var(--text-primary); font-size: 1rem;"
                />
              </div>
              
              <div class="form-group">
                <label for="email" style="color: var(--text-secondary); margin-bottom: 0.5rem; display: block;">Business Email *</label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  placeholder="<EMAIL>"
                  required
                  style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-subtle); border-radius: 10px; background: rgba(255, 255, 255, 0.05); color: var(--text-primary); font-size: 1rem;"
                />
              </div>
            </div>

            <!-- Phone and Website -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
              <div class="form-group">
                <label for="phone" style="color: var(--text-secondary); margin-bottom: 0.5rem; display: block;">Phone Number</label>
                <input
                  type="tel"
                  id="phone"
                  name="phone"
                  placeholder="+**************"
                  style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-subtle); border-radius: 10px; background: rgba(255, 255, 255, 0.05); color: var(--text-primary); font-size: 1rem;"
                />
              </div>
              
              <div class="form-group">
                <label for="website" style="color: var(--text-secondary); margin-bottom: 0.5rem; display: block;">Company Website</label>
                <input
                  type="url"
                  id="website"
                  name="website"
                  placeholder="https://yourcompany.com"
                  style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-subtle); border-radius: 10px; background: rgba(255, 255, 255, 0.05); color: var(--text-primary); font-size: 1rem;"
                />
              </div>
            </div>

            <!-- Company Size and Location -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1rem;">
              <div class="form-group">
                <label for="headcount" style="color: var(--text-secondary); margin-bottom: 0.5rem; display: block;">Company Size *</label>
                <select
                  id="headcount"
                  name="headcount"
                  required
                  style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-subtle); border-radius: 10px; background: rgba(255, 255, 255, 0.05); color: var(--text-primary); font-size: 1rem;"
                >
                  <option value="">Select company size</option>
                  <option value="1-10">1-10 employees</option>
                  <option value="11-50">11-50 employees</option>
                  <option value="51-200">51-200 employees</option>
                  <option value="201-1000">201-1000 employees</option>
                  <option value="1000+">1000+ employees</option>
                </select>
              </div>
              
              <div class="form-group">
                <label for="location" style="color: var(--text-secondary); margin-bottom: 0.5rem; display: block;">Primary Location *</label>
                <input
                  type="text"
                  id="location"
                  name="location"
                  placeholder="City, Country"
                  required
                  style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-subtle); border-radius: 10px; background: rgba(255, 255, 255, 0.05); color: var(--text-primary); font-size: 1rem;"
                />
              </div>

              <div class="form-group">
                <label for="package" style="color: var(--text-secondary); margin-bottom: 0.5rem; display: block;">Package *</label>
                <select
                  id="package"
                  name="package"
                  required
                  style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-subtle); border-radius: 10px; background: rgba(255, 255, 255, 0.05); color: var(--text-primary); font-size: 1rem;"
                >
                  <option value="">Select package</option>
                  <option value="Free">Free</option>
                  <option value="Pro">Pro (€85.00)</option>
                  <option value="Custom">Custom</option>
                </select>
              </div>

              <div class="form-group">
                <label for="taxId" style="color: var(--text-secondary); margin-bottom: 0.5rem; display: block;">Tax Identification Number (NIP/VAT) *</label>
                <input
                  type="text"
                  id="taxId"
                  name="taxId"
                  placeholder="Enter your Tax ID / NIP / VAT number"
                  required
                  style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-subtle); border-radius: 10px; background: rgba(255, 255, 255, 0.05); color: var(--text-primary); font-size: 1rem;"
                />
              </div>
            </div>

            <!-- Company Description -->
            <div class="form-group">
              <label for="description" style="color: var(--text-secondary); margin-bottom: 0.5rem; display: block;">Company Description</label>
              <textarea
                id="description"
                name="description"
                rows="3"
                placeholder="Brief description of your company and hiring needs..."
                style="width: 100%; padding: 0.75rem; border: 1px solid var(--border-subtle); border-radius: 10px; background: rgba(255, 255, 255, 0.05); color: var(--text-primary); font-size: 1rem; resize: vertical;"
              ></textarea>
            </div>

            <!-- Submit Button -->
            <button 
              type="submit" 
              class="btn btn-primary"
              style="padding: 1rem 2rem; font-size: 1.1rem; margin-top: 1rem; width: 100%; justify-self: center;"
            >
              <span class="btn-text">Start Using Canvider ATS</span>
              <span class="btn-loading" style="display: none;">Creating Account...</span>
            </button>
          </form>
        </div>
      </div>
    </section>

    <!-- Benefits Section -->
    <section style="padding: 6rem 5%;">
      <div class="container">
        <div class="section-title" style="text-align: center; margin-bottom: 4rem;">
          <h2 style="color: var(--text-primary); margin-bottom: 1rem;">What You Get</h2>
          <p style="color: var(--text-muted); max-width: 600px; margin: 0 auto;">
            Everything you need to streamline your hiring process and find the perfect candidates.
          </p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
          <div style="text-align: center; padding: 2rem;">
            <div style="width: 60px; height: 60px; background: var(--primary-gradient); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; font-size: 1.5rem;">
              🎯
            </div>
            <h3 style="color: var(--text-primary); margin-bottom: 1rem;">AI-Powered Scoring</h3>
            <p style="color: var(--text-muted);">Automatically score and rank candidates based on job requirements</p>
          </div>

          <div style="text-align: center; padding: 2rem;">
            <div style="width: 60px; height: 60px; background: var(--primary-gradient); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; font-size: 1.5rem;">
              👥
            </div>
            <h3 style="color: var(--text-primary); margin-bottom: 1rem;">Team Collaboration</h3>
            <p style="color: var(--text-muted);">Work together with your team to evaluate and hire the best candidates</p>
          </div>

          <div style="text-align: center; padding: 2rem;">
            <div style="width: 60px; height: 60px; background: var(--primary-gradient); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; font-size: 1.5rem;">
              ⚡
            </div>
            <h3 style="color: var(--text-primary); margin-bottom: 1rem;">Workflow Automation</h3>
            <p style="color: var(--text-muted);">Automate repetitive tasks and focus on what matters most</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Enhanced Footer -->
    <footer class="footer">
      <div class="footer-container">
        <div class="footer-content">
          <!-- Brand Section -->
          <div class="footer-brand">
            <div class="footer-logo">
              <div class="footer-logo-icon">
                <i class="fas fa-chart-bar"></i>
              </div>
              <span class="footer-logo-text">Canvider</span>
            </div>
            <p class="footer-description">
              The intelligent ATS platform that helps you hire better, faster.
            </p>
            <div class="footer-social">
              <a href="https://www.linkedin.com/company/canvider/" class="footer-social-link" aria-label="LinkedIn" target="_blank" rel="noopener noreferrer">
                <i class="fab fa-linkedin"></i>
              </a>
              <a href="https://www.instagram.com/canvider/" class="footer-social-link" aria-label="Instagram" target="_blank" rel="noopener noreferrer">
                <i class="fab fa-instagram"></i>
              </a>
            </div>
          </div>

          <!-- Features Section -->
          <div class="footer-section">
            <h4>Features</h4>
            <ul class="footer-links">
              <li><a href="./canv_ai_score.html">AI Score</a></li>
              <li><a href="./talentpool_feature.html">TalentPool</a></li>
              <li><a href="./expert_support.html">Expert Support</a></li>
              <li><a href="./workflow_automation.html">Workflow Automation</a></li>
              <li><a href="./canvider_jobcraft.html">JobCraft</a></li>
              <li><a href="./collaborative_candidate_assessment.html">Team Assessment</a></li>
            </ul>
          </div>

          <!-- Company Section -->
          <div class="footer-section">
            <h4>Company</h4>
            <ul class="footer-links">
              <li><a href="./about_us.html">About Us</a></li>
              <li>
                <button onclick="scrollToSection('#contact')">Contact</button>
              </li>
              <li><a href="./ats_signup.html">Get Started</a></li>
            </ul>
          </div>

          <!-- Product Section -->
          <div class="footer-section">
            <h4>Product</h4>
            <ul class="footer-links">
              <li>
                <button onclick="scrollToSection('#features')">Features</button>
              </li>
              <li>
                <button onclick="scrollToSection('#pricing')">Pricing</button>
              </li>
              <li><a href="./ats_signup.html">Start Free Trial</a></li>
            </ul>
          </div>

          <!-- Legal Section -->
          <div class="footer-section">
            <h4>Legal</h4>
            <ul class="footer-links">
              <li><a href="./legal/terms-of-service.pdf" target="_blank">Terms of Service</a></li>
              <li><a href="./legal/privacy-policy.pdf" target="_blank">Privacy Policy</a></li>
              <li><a href="./legal/data-processing-agreement.pdf" target="_blank">Data Processing Agreement</a></li>
              <li><a href="./legal/faq-data-ai-usage.pdf" target="_blank">FAQ – Data & AI Usage</a></li>
              <li><a href="./legal/privacy-policy.pdf" target="_blank"></a></li>
            </ul>
          </div>
        </div>

        <div class="footer-bottom">
          <p>&copy; 2024 Canvider. All rights reserved.</p>
          <div class="footer-legal-links">
            <a href="./legal/faq-data-ai-usage.pdf" target="_blank">How we use AI & process data</a>
          </div>
        </div>
      </div>
    </footer>

    <!-- JavaScript for form handling -->
    <script>
    
      // Header background on scroll
      let header = null;
      function updateHeader() {
        if (!header) header = document.querySelector("header");

        if (window.scrollY > 100) {
          header.style.background = "rgba(10, 10, 10, 0.98)";
          header.style.backdropFilter = "blur(25px)";
          header.style.boxShadow = "0 4px 30px rgba(102, 126, 234, 0.1)";
        } else {
          header.style.background = "rgba(10, 10, 10, 0.95)";
          header.style.backdropFilter = "blur(20px)";
          header.style.boxShadow = "none";
        }
      }

      // Throttled scroll event
      let ticking = false;
      function requestUpdate() {
        if (!ticking) {
          requestAnimationFrame(() => {
            updateHeader();
            ticking = false;
          });
          ticking = true;
        }
      }

      window.addEventListener("scroll", requestUpdate, { passive: true });

      // Initial setup
      updateHeader();

      // Form submission handling
      // Form submission handling
      document.getElementById('ats-signup-form').addEventListener('submit', function(e) {
        e.preventDefault();

        // Show loading state
        const submitBtn = this.querySelector('button[type="submit"]');
        const btnText = submitBtn.querySelector('.btn-text');
        const btnLoading = submitBtn.querySelector('.btn-loading');

        btnText.style.display = 'none';
        btnLoading.style.display = 'inline';
        submitBtn.disabled = true;

        // Prepare form data
        const formData = new FormData();
        formData.append('companyName', document.getElementById('companyName').value);
        formData.append('industry', document.getElementById('industry').value);
        formData.append('contactName', document.getElementById('contactName').value);
        formData.append('email', document.getElementById('email').value);
        formData.append('taxId', document.getElementById('taxId').value);
        formData.append('phone', document.getElementById('phone').value);
        formData.append('website', document.getElementById('website').value);
        formData.append('headcount', document.getElementById('headcount').value);
        formData.append('location', document.getElementById('location').value);
        formData.append('package', document.getElementById('package').value);
        formData.append('description', document.getElementById('description').value);

        // Your Google Apps Script Web App URL
        const GOOGLE_SCRIPT_URL = 'https://script.google.com/macros/s/AKfycby70doMvd36Q5e5jk_d1qYneM1pUJyrVU8sZH4wcR34FQAiwBb-TOLZitoNmPkW5vkisg/exec';

        // Submit to Google Apps Script with no-cors mode
        fetch(GOOGLE_SCRIPT_URL, {
          method: 'POST',
          mode: 'no-cors', // This bypasses CORS restrictions
          body: formData
        }).then(() => {
          // Reset button state
          btnText.style.display = 'inline';
          btnLoading.style.display = 'none';
          submitBtn.disabled = false;
          
          // Show success message
          alert('Thank you for your interest! We will set up your Canvider ATS account and contact you within 24 hours.');
          this.reset();
        }).catch(error => {
          console.error('Error submitting form:', error);
          
          // Reset button state
          btnText.style.display = 'inline';
          btnLoading.style.display = 'none';
          submitBtn.disabled = false;
          
          alert('There was an error submitting your form. Please try again or contact us directly.');
        });
      });
      
    </script>
