<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Canvider - AI-Powered Recruitment Platform</title>
    <!-- Font Awesome for icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Bootstrap for responsive design -->
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css"
      rel="stylesheet"
      integrity="sha384-T3c6CoIi6uLrA9TneNEoa7RxnatzjcDSCmG1MXxSR1GAsXEV/Dwwykc2MPK8M2HN"
      crossorigin="anonymous"
    />
    <!-- Modern Design System CSS -->
    <link rel="stylesheet" href="style.css" />
    <!-- favicon -->
    <link rel="icon" type="image/x-icon" href="./Static/favicon.png" />
  </head>
  <body>
    <div class="bg-animation"></div>

    <header class="header">
      <nav class="navbar nav-container">
        <div class="logo">
          <a href="./index.html">
            <img src="./Static/canvider_white.png" alt="logo" height="32" />
          </a>
        </div>
        <div class="menu-toggle" onclick="toggleMenu()">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <div class="nav-content">
          <ul class="nav-links my-auto">
            <li><a href="#features">Features</a></li>
            <li><a href="#pricing">Pricing</a></li>
            <li><a href="./about_us.html">About</a></li>
            <li><a href="#contact">Contact</a></li>
          </ul>
          <div class="nav-buttons">
            <a href="./ats_signup.html" class="btn btn-primary">Get Started</a>
          </div>
        </div>
      </nav>
    </header>

    <script>
      function toggleMenu() {
        document.querySelector('.nav-content').classList.toggle('active');
      }

      window.addEventListener('scroll', () => {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
          header.classList.add('scrolled');
        } else {
          header.classList.remove('scrolled');
        }
      });
    </script>

    <section class="hero">
      <!-- Geometric Grid Background -->
      <div class="geometric-grid">
        <!-- Horizontal lines -->
        <div class="grid-line horizontal"></div>
        <div class="grid-line horizontal"></div>
        <div class="grid-line horizontal"></div>
        <div class="grid-line horizontal"></div>
        <div class="grid-line horizontal"></div>
        <div class="grid-line horizontal"></div>

        <!-- Vertical lines -->
        <div class="grid-line vertical"></div>
        <div class="grid-line vertical"></div>
        <div class="grid-line vertical"></div>
        <div class="grid-line vertical"></div>
        <div class="grid-line vertical"></div>
        <div class="grid-line vertical"></div>

        <!-- Grid intersection points -->
        <div class="grid-intersection"></div>
        <div class="grid-intersection"></div>
        <div class="grid-intersection"></div>
        <div class="grid-intersection"></div>
      </div>

      <div class="hero-content">
        <h1>Streamline Your Hiring with AI</h1>
        <p>
          Canvider combines cutting-edge AI technology with human expertise to
          streamline your recruitment process, score candidates intelligently,
          and connect you with top talent faster than ever.
        </p>
        <div class="hero-buttons">
          <a href="#features" class="btn btn-secondary btn-hero"
            >Explore Features</a>
          <a href="./ats_signup.html" class="btn btn-primary btn-hero">Start Today</a>
          <a href="https://youtu.be/Tv07itlHD3c?si=cWacttTCCBFGPsZd" target="_blank" class="btn btn-secondary btn-hero">
          Watch Demo
          </a>
        </div>
      </div>
    </section>

    <section id="features" class="features">
      <div class="container">
        <div class="section-title fade-in">
          <h2>Powerful Features</h2>
          <p>
            Everything you need to transform your recruitment process and find
            the perfect candidates
          </p>
        </div>

        <div class="features-grid">
          <div class="feature-card fade-in">
            <div class="feature-icon">🎯</div>
            <h3>Canvider AI Score</h3>
            <p>
              Score Your Candidates with AI. When there are many candidates
              applying for a position, Canvider AI Score uses AI to evaluate
              each candidate's resume and score them based on how well they
              match the job description. Save hours of manual screening and
              focus on the best candidates.
            </p>

            <div class="feature-additional">
              <h4>Key Benefits:</h4>
              <ul>
                <li>Save time evaluating candidates</li>
                <li>Sort out the best candidates automatically</li>
                <li>
                  Observe AI-generated highlights
                </li>
                <li>Quickly identify the best matches</li>
              </ul>
            </div>

            <div class="feature-cta">
              <a href="./canv_ai_score.html">Learn More</a>
              <a href="./legal/faq-data-ai-usage.pdf" target="_blank" class="ai-data-link">How we use AI & process data</a>
            </div>
          </div>

          <div class="feature-card fade-in">
            <div class="feature-icon">👥</div>
            <h3>Canvider TalentPool</h3>
            <p>
              Optimize Your Hiring with Precision Matching. Discover ideal
              candidates quickly with Canvider TalentPool. Our extensive
              database connects you with top talent from various industries,
              streamlining your hiring process.
            </p>

            <div class="feature-additional">
              <h4>TalentPool Features:</h4>
              <ul>
                <li>Access a wide talent database</li>
                <li>Smart matching algorithms</li>
                <li>Industry-specific talent pools</li>
                <li>Advanced search and filtering</li>
                <li>Pre-screened candidate profiles</li>
              </ul>
            </div>

            <div class="feature-cta">
              <a href="./talentpool_feature.html">Explore Database</a>
            </div>
          </div>

          <div class="feature-card fade-in">
            <div class="feature-icon">🎓</div>
            <h3>Human Expert Support</h3>
            <p>
              Get second opinions from our vetted experts when you need them.
              Speed up technical interviews and ensure you're making the best
              hiring decisions.
            </p>

            <div class="feature-additional">
              <h4>Expert Services:</h4>
              <ul>
                <li>Technical interview support</li>
                <li>Industry-specific expertise</li>
                <li>24/7 consultation available</li>
                <li>Skill assessment validation</li>
                <li>Cultural fit evaluation</li>
              </ul>
            </div>

            <div class="feature-cta">
              <a href="./expert_support.html">Connect with Experts</a>
            </div>
          </div>

          <div class="feature-card fade-in">
            <div class="feature-icon">👥</div>
            <h3>Collaborative Candidate Assessment</h3>
            <p>
              Enable your team to work together seamlessly when evaluating candidates.
              Share insights, add comments, and make informed hiring decisions with
              transparent collaboration tools.
            </p>

            <div class="feature-additional">
              <h4>Collaboration Features:</h4>
              <ul>
                <li>Internal comments and notes</li>
                <li>Team member task assignments</li>
                <li>Decision tracking and audit trails</li>
                <li>Multi-reviewer evaluations</li>
                <li>Transparent hiring process</li>
              </ul>
            </div>

            <div class="feature-cta">
              <a href="./collaborative_candidate_assessment.html">Learn More</a>
            </div>
          </div>

          <div class="feature-card fade-in">
            <div class="feature-icon">⚡</div>
            <h3>Workflow Automation</h3>
            <p>
              Automate repetitive tasks like sending emails, tracking
              interviews, and updating candidate statuses. Manage everything
              from one centralized platform.
            </p>

            <div class="feature-additional">
              <h4>Automation Features:</h4>
              <ul>
                <li>Email template automation</li>
                <li>Interview scheduling</li>
                <li>Status update notifications</li>
                <li>Candidate pipeline management</li>
                <li>Custom workflow builder</li>
              </ul>
            </div>

            <div class="feature-cta">
              <a href="./workflow_automation.html">See Workflows</a>
            </div>
          </div>

          <div class="feature-card fade-in">
            <div class="feature-icon">✍️</div>
            <h3>Canvider JobCraft</h3>
            <p>
              Perfect Job Descriptions with a Single Click. Automatically
              generate Job Descriptions using our AI-powered templates that fit
              your Company Brand like a glove. Save time while attracting the
              right candidates.
            </p>

            <div class="feature-additional">
              <h4>JobCraft Features:</h4>
              <ul>
                <li>AI-powered job description generator</li>
                <li>Company brand and values integration</li>
                <li>Industry-specific templates</li>
                <li>SEO optimization for job boards</li>
                <li>One-click generation process</li>
              </ul>
            </div>

            <div class="feature-cta">
              <a href="./canvider_jobcraft.html">Create Job Description</a>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Divider -->
    <div class="section-divider"></div>

    <!-- Why Choose Canvider Section -->
    <section id="benefits" class="benefits-section">
      <div class="container">
        <div class="benefits-grid">
          <div class="benefits-content fade-in">
            <h2>Why Choose Canvider?</h2>
            <div class="benefits-list">
              <div class="benefit-item fade-in" style="animation-delay: 0.1s">
                <div class="benefit-check">
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22,4 12,14.01 9,11.01"></polyline>
                  </svg>
                </div>
                <div class="benefit-text">
                  <h3>Reduce Time-to-Hire significantly</h3>
                  <p>
                    Streamlined workflows and automation help you hire faster
                    than ever
                  </p>
                </div>
              </div>

              <div class="benefit-item fade-in" style="animation-delay: 0.2s">
                <div class="benefit-check">
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22,4 12,14.01 9,11.01"></polyline>
                  </svg>
                </div>
                <div class="benefit-text">
                  <h3>Improve Candidate Quality</h3>
                  <p>
                    AI-powered matching ensures you get the best candidates for
                    each role
                  </p>
                </div>
              </div>

              <div class="benefit-item fade-in" style="animation-delay: 0.3s">
                <div class="benefit-check">
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22,4 12,14.01 9,11.01"></polyline>
                  </svg>
                </div>
                <div class="benefit-text">
                  <h3>Enhanced Team Productivity</h3>
                  <p>
                    Collaborative tools and automated workflows boost your
                    team's efficiency
                  </p>
                </div>
              </div>

              <div class="benefit-item fade-in" style="animation-delay: 0.4s">
                <div class="benefit-check">
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                    <polyline points="22,4 12,14.01 9,11.01"></polyline>
                  </svg>
                </div>
                <div class="benefit-text">
                  <h3>Seamless Integration</h3>
                  <p>
                    Connect with your existing HR tools and job boards
                    effortlessly
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="benefits-image fade-in">
            <div class="image-container">
              <img
                src="./Static/Illustrations/illustrationNewColor_2.png"
                alt="Business professionals in recruitment meeting"
              />
              <div class="decoration-1"></div>
              <div class="decoration-2"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Divider -->
    <div class="section-divider"></div>

    <section id="pricing" class="pricing">
      <div class="container">
        <div class="section-title fade-in">
          <h2>Simple, Transparent Pricing</h2>
          <p>Choose the plan that fits your hiring needs and company size</p>
        </div>

        <div class="pricing-grid">
          <div class="pricing-card fade-in">
            <h3>Simple</h3>
            <div class="price">Free</div>
            <div class="price-period">not billed</div>
            <div class="pricing-description">
              For small businesses that open few positions per year and do not
              share the ATS needs of bigger companies.
            </div>
            <ul class="features-list">
              <li>All of the main ATS Features</li>
              <li>Up to 10 Users</li>
              <li>Up to 5 Listings per year</li>
            </ul>
            <div class="pricing-divider"></div>
            <div class="access-title">Access to:</div>
            <ul class="features-list premium">
              <li><strong>AI Powered Scoring</strong></li>
              <li class="not-available"><strong>Canvider Candidate Pool</strong></li>
              <li class="not-available"><strong>Human Expert Support</strong></li>
            </ul>
            <a
              href="./ats_signup.html"
              class="btn btn-primary"
              style="margin-top: 1rem; width: 100%"
              >Start Today</a
            >
          </div>

          <div class="pricing-card featured fade-in">
            <h3>Pro</h3>
            <div class="price">€85.00</div>
            <div class="price-period">billed Monthly</div>
            <div class="pricing-description">
              For growing businesses that need a reliable and scalable hiring
              solution. Streamlining the process of finding top talent
              efficiently.
            </div>
            <ul class="features-list">
              <li>All of the main ATS Features</li>
              <li>Up to 25 Users</li>
              <li>Up to 25 Listings per month</li>
            </ul>
            <div class="pricing-divider"></div>
            <div class="access-title">Access to:</div>
            <ul class="features-list premium">
              <li><strong>AI Powered Scoring</strong></li>
              <li><strong>Canvider Candidate Pool</strong></li>
              <li class="not-available"><strong>Human Expert Support</strong></li>
            </ul>
            <a
              href="./ats_signup.html"
              class="btn btn-primary"
              style="margin-top: 1rem; width: 100%"
              >Start Today</a
            >
          </div>

          <div class="pricing-card fade-in">
            <h3>Enterprise</h3>
            <div class="price">Custom</div>
            <div class="price-period">billed as negotiated</div>
            <div class="pricing-description">
              For businesses that aim to consistently hire talent and grow
              swiftly without worrying about the hiring procedures.
            </div>
            <ul class="features-list">
              <li>All of the main ATS Features</li>
              <li>As many Users as needed</li>
              <li>As many Listings as needed</li>
            </ul>
            <div class="pricing-divider"></div>
            <div class="access-title">Access to:</div>
            <ul class="features-list premium">
              <li><strong>AI Powered Scoring</strong></li>
              <li><strong>Canvider Candidate Pool</strong></li>
              <li><strong>Human Expert Support</strong></li>
            </ul>
            <a
              href="#contact"
              class="btn btn-primary"
              style="margin-top: 1rem; width: 100%"
              >Contact us</a
            >
          </div>
        </div>
      </div>
    </section>

    <!-- Divider -->
    <div class="section-divider"></div>

    <!-- ATS Comparison Table Section -->
    <section id="comparison" class="features comparison-section">
      <div class="container">
        <div class="section-title fade-in">
          <h2>How Canvider Compares?</h2>
          <p>Compare Canvider with other leading ATS platforms</p>
        </div>
        <div class="comparison-wrapper fade-in">
            <div class="table-responsive">
                <table class="table table-dark table-bordered">
                    <thead>
                        <tr>
                            <th class="feature-header text-center">Feature / Metric</th>
                            <th class="dark-cell text-center">Canvider</th>
                            <th class="text-center d-none d-md-table-cell">Greenhouse</th>
                            <th class="text-center d-none d-md-table-cell">Workable</th>
                            <th class="text-center d-none d-md-table-cell">SmartRecruiters</th>
                        </tr>
                    </thead>
                    <tbody class="text-center">
                        <tr>
                            <td class="feature-name">Starting Price</td>
                            <td class="dark-cell text-success">Free Forever</td>
                            <td class="expensive d-none d-md-table-cell">from ~€500/mo</td>
                            <td class="moderate d-none d-md-table-cell">~€330/mo</td>
                            <td class="expensive d-none d-md-table-cell">~€1000/mo</td>
                        </tr>
                        <tr>
                            <td class="feature-name">AI Features</td>
                            <td class="dark-cell text-success">Yes</td>
                            <td class="text-success d-none d-md-table-cell">Yes</td>
                            <td class="text-success d-none d-md-table-cell">Yes</td>
                            <td class="text-success d-none d-md-table-cell">Yes</td>
                        </tr>
                        <tr>
                            <td class="feature-name">Communication</td>
                            <td class="dark-cell text-success">Yes</td>
                            <td class="text-success d-none d-md-table-cell">Yes</td>
                            <td class="text-success d-none d-md-table-cell">Yes</td>
                            <td class="text-success d-none d-md-table-cell">Yes</td>
                        </tr>
                        <tr>
                            <td class="feature-name">Collaboration</td>
                            <td class="dark-cell text-success">Yes</td>
                            <td class="text-success d-none d-md-table-cell">Yes</td>
                            <td class="text-success d-none d-md-table-cell">Yes</td>
                            <td class="text-success d-none d-md-table-cell">Yes</td>
                        </tr>
                        <tr>
                            <td class="feature-name">Ease of Use</td>
                            <td class="dark-cell">Simple</td>
                            <td class="d-none d-md-table-cell">Complex</td>
                            <td class="d-none d-md-table-cell">Intuitive</td>
                            <td class="d-none d-md-table-cell">Modern</td>
                        </tr>
                        <tr>
                            <td class="feature-name">Free Trial</td>
                            <td class="dark-cell text-success">Forever</td>
                            <td class="text-danger d-none d-md-table-cell">No</td>
                            <td class="text-warning d-none d-md-table-cell">15 days</td>
                            <td class="text-danger d-none d-md-table-cell">No</td>
                        </tr>
                        <tr>
                            <td class="feature-name">Analytics</td>
                            <td class="dark-cell text-success">Yes</td>
                            <td class="text-success d-none d-md-table-cell">Yes</td>
                            <td class="text-success d-none d-md-table-cell">Yes</td>
                            <td class="text-success d-none d-md-table-cell">Yes</td>
                        </tr>
                        <tr>
                            <td class="feature-name">Best For</td>
                            <td class="dark-cell">SMBs & Startups</td>
                            <td class="d-none d-md-table-cell">High-growth</td>
                            <td class="d-none d-md-table-cell">Scale-ups</td>
                            <td class="d-none d-md-table-cell">Enterprise</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <p style="font-size: 0.8rem; color: var(--text-muted); margin-top: 1rem;">
                *This comparison is based on publicly available information as of July 2025. Features and pricing may change over time.
                <span class="d-md-none text-success"><br><i>flip your phone sideways for full view.<i></span>
            </p>
        </div>
      </div>
    </section>

    <!-- Divider -->
    <div class="section-divider"></div>

    <section id="testimonials" class="testimonials" style="display: none;">
      <div class="container">
        <div class="section-title fade-in">
          <h2>What Our Customers Say</h2>
          <p>
            Join thousands of companies who trust Canvider to revolutionize
            their hiring process
          </p>
        </div>

        <div class="testimonials-grid">
          <div class="testimonial-card fade-in" data-delay="0">
            <div class="stars">
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
            </div>
            <blockquote>
              "Canvider has revolutionized our hiring process. We've reduced our
              time-to-hire by 60% and improved candidate quality significantly."
            </blockquote>
            <div class="testimonial-author">
              <img
                src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100"
                alt="Sarah Johnson"
                class="author-image"
              />
              <div class="author-info">
                <div class="author-name">Sarah Johnson</div>
                <div class="author-role">HR Director, TechCorp</div>
              </div>
            </div>
          </div>

          <div class="testimonial-card fade-in" data-delay="0.1">
            <div class="stars">
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
            </div>
            <blockquote>
              "The AI-powered features are incredible. It's like having an extra
              team member who never sleeps. Our recruitment efficiency has
              skyrocketed."
            </blockquote>
            <div class="testimonial-author">
              <img
                src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100"
                alt="Michael Chen"
                class="author-image"
              />
              <div class="author-info">
                <div class="author-name">Michael Chen</div>
                <div class="author-role">CEO, StartupXYZ</div>
              </div>
            </div>
          </div>

          <div class="testimonial-card fade-in" data-delay="0.2">
            <div class="stars">
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
              <span class="star">★</span>
            </div>
            <blockquote>
              "The collaboration features are fantastic. Our entire hiring team
              can work together seamlessly, and the analytics help us make
              data-driven decisions."
            </blockquote>
            <div class="testimonial-author">
              <img
                src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100"
                alt="Emily Rodriguez"
                class="author-image"
              />
              <div class="author-info">
                <div class="author-name">Emily Rodriguez</div>
                <div class="author-role">Talent Manager, GlobalTech</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Divider -->
    <div class="section-divider"></div>

    <!-- Contact Section -->
    <section id="contact" class="contact-section">
      <div class="container">
        <div class="contact-grid">
          <!-- Left Column - Contact Info -->
          <div class="contact-info fade-in">
            <h2>Ready to Transform Your Hiring?</h2>
            <p>
              Get started with Canvider today and see the difference intelligent
              recruitment can make.
            </p>

            <div class="contact-items">
              <div class="contact-item fade-in" style="animation-delay: 0.2s">
                <div class="contact-icon">
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                  >
                    <path
                      d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"
                    ></path>
                    <polyline points="22,6 12,13 2,6"></polyline>
                  </svg>
                </div>
                <div class="contact-details">
                  <div class="contact-title">Email</div>
                  <div class="contact-value"><EMAIL></div>
                </div>
              </div>
            </div>
          </div>

          <!-- Right Column - Contact Form -->
          <div class="contact-form-wrapper fade-in">
            <div class="contact-card">
              <h3>Contact us</h3>
              <form id="contact-form" class="contact-form">
                <div class="form-grid">
                  <div class="form-group">
                    <label for="firstName">First Name</label>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      placeholder="John"
                      required
                    />
                    <span class="error-message"></span>
                  </div>
                  <div class="form-group">
                    <label for="lastName">Last Name</label>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      placeholder="Doe"
                      required
                    />
                    <span class="error-message"></span>
                  </div>
                </div>

                <div class="form-group">
                  <label for="email">Company Email</label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    placeholder="<EMAIL>"
                    required
                  />
                  <span class="error-message"></span>
                </div>

                <div class="form-group">
                  <label for="company">Company Name</label>
                  <input
                    type="text"
                    id="company"
                    name="company"
                    placeholder="Your Company"
                    required
                  />
                  <span class="error-message"></span>
                </div>

                <div class="form-group">
                  <label for="message">Message</label>
                  <textarea
                    id="message"
                    name="message"
                    rows="4"
                    placeholder="Tell us about your hiring needs..."
                    required
                  ></textarea>
                  <span class="error-message"></span>
                </div>

                <button type="submit" class="btn-submit">
                  <span class="btn-text">Submit</span>
                  <span class="btn-loading" style="display: none"
                    >Submitting...</span
                  >
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Enhanced Footer -->
    <footer class="footer">
      <div class="footer-container">
        <div class="footer-content">
          <!-- Brand Section -->
          <div class="footer-brand">
            <div class="footer-logo">
              <div class="footer-logo-icon">
                <i class="fas fa-chart-bar"></i>
              </div>
              <span class="footer-logo-text">Canvider</span>
            </div>
            <p class="footer-description">
              The intelligent ATS platform that helps you hire better, faster.
            </p>
            <div class="footer-social">
              
              <a href="https://www.linkedin.com/company/canvider/" class="footer-social-link" aria-label="LinkedIn" target="_blank" rel="noopener noreferrer">
                <i class="fab fa-linkedin"></i>
              </a>
              <a href="https://www.instagram.com/canvider/" class="footer-social-link" aria-label="Instagram" target="_blank" rel="noopener noreferrer">
                <i class="fab fa-instagram"></i>
              </a>
            </div>
          </div>

          <!-- Features Section -->
          <div class="footer-section">
            <h4>Features</h4>
            <ul class="footer-links">
              <li><a href="./canv_ai_score.html">AI Score</a></li>
              <li><a href="./talentpool_feature.html">TalentPool</a></li>
              <li><a href="./expert_support.html">Expert Support</a></li>
              <li><a href="./workflow_automation.html">Workflow Automation</a></li>
              <li><a href="./canvider_jobcraft.html">JobCraft</a></li>
              <li><a href="./collaborative_candidate_assessment.html">Team Assessment</a></li>
            </ul>
          </div>

          <!-- Company Section -->
          <div class="footer-section">
            <h4>Company</h4>
            <ul class="footer-links">
              <li><a href="./about_us.html">About Us</a></li>
              <li>
                <button onclick="scrollToSection('#contact')">Contact</button>
              </li>
              <li><a href="./ats_signup.html">Get Started</a></li>
            </ul>
          </div>

          <!-- Product Section -->
          <div class="footer-section">
            <h4>Product</h4>
            <ul class="footer-links">
              <li>
                <button onclick="scrollToSection('#features')">Features</button>
              </li>
              <li>
                <button onclick="scrollToSection('#pricing')">Pricing</button>
              </li>
              <li><a href="./ats_signup.html">Start Today</a></li>
            </ul>
          </div>

          <!-- Legal Section -->
          <div class="footer-section">
            <h4>Legal</h4>
            <ul class="footer-links">
              <li><a href="./legal/terms-of-service.pdf" target="_blank">Terms of Service</a></li>
              <li><a href="./legal/privacy-policy.pdf" target="_blank">Privacy Policy</a></li>
              <li><a href="./legal/data-processing-agreement.pdf" target="_blank">Data Processing Agreement</a></li>
              <li><a href="./legal/faq-data-ai-usage.pdf" target="_blank">FAQ – Data & AI Usage</a></li>
              <li><a href="./legal/privacy-policy.pdf" target="_blank"></a></li>
            </ul>
          </div>
        </div>

        <div class="footer-bottom">
          <p>&copy; 2024 Canvider. All rights reserved.</p>
          <div class="footer-legal-links">
            <a href="./legal/faq-data-ai-usage.pdf" target="_blank">How we use AI & process data</a>
          </div>
        </div>
      </div>
    </footer>

    <script>
      // Scroll to section function
      function scrollToSection(href) {
        if (href.startsWith("#")) {
          const element = document.getElementById(href.substring(1));
          if (element) {
            element.scrollIntoView({ behavior: "smooth" });
          }
        }
      }

      // Optimized Geometric Grid Animation Control
      let gridElements = null;
      function initGridElements() {
        if (!gridElements) {
          gridElements = {
            lines: document.querySelectorAll(".grid-line"),
            intersections: document.querySelectorAll(".grid-intersection"),
            hero: document.querySelector(".hero"),
          };
        }
        return gridElements;
      }

      function updateGridAnimation() {
        const elements = initGridElements();
        const scrolled = window.pageYOffset;
        const heroHeight = elements.hero.offsetHeight;

        // Only apply effects when hero is visible
        if (scrolled < heroHeight) {
          // Pulse intersections based on scroll with optimized calculation
          const scrollFactor = scrolled * 0.01;
          elements.intersections.forEach((intersection, index) => {
            const pulseIntensity = Math.sin(scrollFactor + index) * 0.2 + 0.8;
            intersection.style.opacity = pulseIntensity;
          });
        }
      }

      // Smooth scrolling
      document.querySelectorAll('a[href^="#"]').forEach((anchor) => {
        anchor.addEventListener("click", function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute("href"));
          if (target) {
            target.scrollIntoView({
              behavior: "smooth",
              block: "start",
            });
          }
        });
      });

      // Scroll animations
      const observerOptions = {
        threshold: 0.1,
        rootMargin: "0px 0px -50px 0px",
      };

      const observer = new IntersectionObserver((entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("visible");
          }
        });
      }, observerOptions);

      document.querySelectorAll(".fade-in").forEach((el) => {
        observer.observe(el);
      });

      // Header background on scroll (integrated with grid animation)
      let header = null;
      function updateHeaderAndGrid() {
        if (!header) header = document.querySelector("header");

        if (window.scrollY > 100) {
          header.style.background = "rgba(10, 10, 10, 0.98)";
          header.style.backdropFilter = "blur(25px)";
          header.style.boxShadow = "0 4px 30px rgba(102, 126, 234, 0.1)";
        } else {
          header.style.background = "rgba(10, 10, 10, 0.95)";
          header.style.backdropFilter = "blur(20px)";
          header.style.boxShadow = "none";
        }

        // Update grid animation in the same function to avoid multiple scroll listeners
        updateGridAnimation();
      }

      // Initial setup
      updateHeaderAndGrid();

      // Add stagger animation to feature cards
      const featureCards = document.querySelectorAll(".feature-card");
      featureCards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
      });

      // Pricing card hover effects
      document.querySelectorAll(".pricing-card").forEach((card) => {
        card.addEventListener("mouseenter", function () {
          this.style.transform = this.classList.contains("featured")
            ? "scale(1.05) translateY(-10px)"
            : "translateY(-10px)";
        });

        card.addEventListener("mouseleave", function () {
          this.style.transform = this.classList.contains("featured")
            ? "scale(1.05)"
            : "none";
        });
      });

      // Feature card expansion effects
      document.querySelectorAll(".feature-card").forEach((card) => {
        let expandTimeout;

        card.addEventListener("mouseenter", function () {
          clearTimeout(expandTimeout);
          expandTimeout = setTimeout(() => {
            this.classList.add("expanded");
          }, 50);
        });

        card.addEventListener("mouseleave", function () {
          clearTimeout(expandTimeout);
          this.classList.remove("expanded");
        });
      });

      // Contact form handling
      document
        .getElementById("contact-form")
        .addEventListener("submit", function (e) {
          e.preventDefault();

          // Show loading state
          const submitBtn = this.querySelector(".btn-submit");
          const btnText = submitBtn.querySelector(".btn-text");
          const btnLoading = submitBtn.querySelector(".btn-loading");

          btnText.style.display = "none";
          btnLoading.style.display = "inline";
          submitBtn.disabled = true;

          // Prepare form data
          const formData = new FormData();
          formData.append('firstName', document.getElementById('firstName').value);
          formData.append('lastName', document.getElementById('lastName').value);
          formData.append('email', document.getElementById('email').value);
          formData.append('company', document.getElementById('company').value);
          formData.append('message', document.getElementById('message').value);

          // Replace this URL with your Contact Form Google Apps Script Web App URL
          const CONTACT_SCRIPT_URL = 'https://script.google.com/macros/s/AKfycbzpULU9Gpv-eMbzXlGLkxK6uJdywqht88-by8Yj3A8v-zxnsG4_Mvl78eKQJfepQoP8Gw/exec';

          // Submit to Google Apps Script with no-cors mode
          fetch(CONTACT_SCRIPT_URL, {
            method: 'POST',
            mode: 'no-cors', // This bypasses CORS restrictions
            body: formData
          }).then(() => {
            // Reset button state
            btnText.style.display = "inline";
            btnLoading.style.display = "none";
            submitBtn.disabled = false;
            
            // Show success message
            alert("Thank you for your interest! We'll get back to you soon.");
            this.reset();
          }).catch(error => {
            console.error('Error submitting contact form:', error);
            
            // Reset button state
            btnText.style.display = "inline";
            btnLoading.style.display = "none";
            submitBtn.disabled = false;
            
            alert('There was an error submitting your message. Please try again or contact us directly.');
          });
        });

      // Optimized performance for combined header and grid animation
      let ticking = false;
      let lastScrollTime = 0;

      function requestCombinedUpdate() {
        const now = performance.now();
        if (!ticking && now - lastScrollTime > 16) {
          // Throttle to ~60fps
          requestAnimationFrame(() => {
            updateHeaderAndGrid();
            ticking = false;
            lastScrollTime = now;
          });
          ticking = true;
        }
      }

      // Single optimized scroll event listener for all scroll-based animations
      window.addEventListener("scroll", requestCombinedUpdate, {
        passive: true,
      });

      // Pause animations when tab is not visible for performance
      document.addEventListener("visibilitychange", function () {
        const gridLines = document.querySelectorAll(".grid-line");
        const intersections = document.querySelectorAll(".grid-intersection");

        if (document.hidden) {
          gridLines.forEach(
            (line) => (line.style.animationPlayState = "paused")
          );
          intersections.forEach(
            (intersection) => (intersection.style.animationPlayState = "paused")
          );
        } else {
          gridLines.forEach(
            (line) => (line.style.animationPlayState = "running")
          );
          intersections.forEach(
            (intersection) =>
              (intersection.style.animationPlayState = "running")
          );
        }
      });
    </script>
  </body>
</html>
