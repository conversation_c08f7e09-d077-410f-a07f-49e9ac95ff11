<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>About Canvider | Revolutionizing Talent Acquisition</title>
    <!-- Font Awesome for icons -->
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <!-- Modern Design System CSS -->
    <link rel="stylesheet" href="style.css" />
    <!-- favicon -->
    <link rel="icon" type="image/x-icon" href="./Static/favicon.png" />
  </head>
  <body>
    <div class="bg-animation"></div>

    <header class="header">
      <nav class="navbar nav-container">
        <div class="logo">
          <a href="./index.html">
            <img src="./Static/canvider_white.png" alt="logo" height="32" />
          </a>
        </div>
        <div class="menu-toggle" onclick="toggleMenu()">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <div class="nav-content">
          <ul class="nav-links">
            <li><a href="./index.html#features">Features</a></li>
            <li><a href="./index.html#pricing">Pricing</a></li>
            <li><a href="./about_us.html">About</a></li>
            <li><a href="./index.html#contact">Contact</a></li>
          </ul>
          <div class="nav-buttons">
            <a href="./ats_signup.html" class="btn btn-secondary">Get Started</a>
            
          </div>
        </div>
      </nav>
    </header>

    <script>
      function toggleMenu() {
        document.querySelector('.nav-content').classList.toggle('active');
      }

      window.addEventListener('scroll', () => {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
          header.classList.add('scrolled');
        } else {
          header.classList.remove('scrolled');
        }
      });
    </script>

    <!-- Hero Section -->
    <section class="hero" style="min-height: 70vh;">
      <div class="hero-content">
        <div style="display: inline-block; background: var(--primary-gradient); padding: 0.5rem 1rem; border-radius: 50px; color: white; font-size: 0.9rem; font-weight: 600; margin-bottom: 1.5rem;">
          🚀 About Canvider
        </div>
        <h1 style="font-size: clamp(2.5rem, 6vw, 4rem);">Revolutionizing Talent Acquisition</h1>
        <p style="font-size: 1.2rem; max-width: 700px; margin: 0 auto 2rem;">
          We're on a mission to simplify recruitment, making it more efficient,
          transparent, and fair for both employers and candidates through the power of AI.
        </p>
      </div>
    </section>

    <!-- Mission Section -->
    <section style="padding: 6rem 5%; background: rgba(10, 10, 10, 0.5);">
      <div class="container" style="max-width: 1000px;">
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 4rem; align-items: center;">
          <div>
            <h2 style="color: var(--text-primary); font-size: 2.5rem; margin-bottom: 2rem;">Our Mission</h2>
            <p style="color: var(--text-muted); font-size: 1.1rem; line-height: 1.8; margin-bottom: 2rem;">
              Canvider is a leading AI-powered talent acquisition platform designed to revolutionize
              the way businesses find, attract, and hire top talent. Our mission is to simplify the
              recruitment process, making it more efficient, transparent, and fair for both employers and candidates.
            </p>
            <p style="color: var(--text-muted); font-size: 1.1rem; line-height: 1.8;">
              At Canvider, we understand the challenges faced by recruiters and hiring managers.
              Traditional recruitment methods are often time-consuming, biased, and lack transparency.
              We believe that technology can play a pivotal role in overcoming these challenges.
            </p>
          </div>
          <div style="text-align: center;">
            <div style="width: 200px; height: 200px; background: var(--primary-gradient); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto; font-size: 4rem;">
              🎯
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Values Section -->
    <section style="padding: 6rem 5%;">
      <div class="container">
        <div class="section-title" style="text-align: center; margin-bottom: 4rem;">
          <h2 style="color: var(--text-primary); margin-bottom: 1rem;">Our Values</h2>
          <p style="color: var(--text-muted); max-width: 600px; margin: 0 auto;">
            The principles that guide everything we do at Canvider.
          </p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
          <!-- Value 1 -->
          <div style="background: var(--surface-dark); border: 1px solid var(--border-subtle); border-radius: 20px; padding: 2.5rem; text-align: center; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 30px rgba(102, 126, 234, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
            <div style="width: 60px; height: 60px; background: var(--primary-gradient); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; font-size: 1.5rem;">
              🤝
            </div>
            <h3 style="color: var(--text-primary); margin-bottom: 1rem; font-size: 1.4rem;">Transparency</h3>
            <p style="color: var(--text-muted); line-height: 1.6;">
              We believe in open, honest communication and transparent processes that build trust between employers and candidates.
            </p>
          </div>

          <!-- Value 2 -->
          <div style="background: var(--surface-dark); border: 1px solid var(--border-subtle); border-radius: 20px; padding: 2.5rem; text-align: center; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 30px rgba(102, 126, 234, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
            <div style="width: 60px; height: 60px; background: var(--primary-gradient); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; font-size: 1.5rem;">
              ⚖️
            </div>
            <h3 style="color: var(--text-primary); margin-bottom: 1rem; font-size: 1.4rem;">Fairness</h3>
            <p style="color: var(--text-muted); line-height: 1.6;">
              Our AI-powered platform eliminates bias and ensures every candidate gets a fair chance based on their skills and qualifications.
            </p>
          </div>

          <!-- Value 3 -->
          <div style="background: var(--surface-dark); border: 1px solid var(--border-subtle); border-radius: 20px; padding: 2.5rem; text-align: center; transition: transform 0.3s ease, box-shadow 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 15px 30px rgba(102, 126, 234, 0.2)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'">
            <div style="width: 60px; height: 60px; background: var(--primary-gradient); border-radius: 15px; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; font-size: 1.5rem;">
              🚀
            </div>
            <h3 style="color: var(--text-primary); margin-bottom: 1rem; font-size: 1.4rem;">Innovation</h3>
            <p style="color: var(--text-muted); line-height: 1.6;">
              We continuously push the boundaries of what's possible in recruitment technology to deliver cutting-edge solutions.
            </p>
          </div>
        </div>
      </div>
    </section>


    <!-- Call to Action Section -->
    <section style="padding: 6rem 5%; background: var(--surface-dark); text-align: center;">
      <div class="container" style="max-width: 800px;">
        <h2 style="color: var(--text-primary); font-size: 2.5rem; margin-bottom: 1rem;">
          Ready to Transform Your Hiring?
        </h2>
        <p style="color: var(--text-muted); font-size: 1.2rem; margin-bottom: 3rem; line-height: 1.6;">
          Join hundreds of companies using Canvider to revolutionize their talent acquisition process.
        </p>

        <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap; margin-bottom: 3rem;">
          <a href="./ats_signup.html" class="btn btn-primary btn-hero">Start Today</a>
          <a href="./index.html#contact" class="btn btn-secondary btn-hero">Contact Us</a>
        </div>

      </div>
    </section>

    <!-- Enhanced Footer -->
    <footer class="footer">
      <div class="footer-container">
        <div class="footer-content">
          <!-- Brand Section -->
          <div class="footer-brand">
            <div class="footer-logo">
              <div class="footer-logo-icon">
                <i class="fas fa-chart-bar"></i>
              </div>
              <span class="footer-logo-text">Canvider</span>
            </div>
            <p class="footer-description">
              The intelligent ATS platform that helps you hire better, faster.
            </p>
            <div class="footer-social">
              
              <a href="https://www.linkedin.com/company/canvider/" class="footer-social-link" aria-label="LinkedIn" target="_blank" rel="noopener noreferrer">
                <i class="fab fa-linkedin"></i>
              </a>
              <a href="https://www.instagram.com/canvider/" class="footer-social-link" aria-label="Instagram" target="_blank" rel="noopener noreferrer">
                <i class="fab fa-instagram"></i>
              </a>
            </div>
          </div>

          <!-- Features Section -->
          <div class="footer-section">
            <h4>Features</h4>
            <ul class="footer-links">
              <li><a href="./canv_ai_score.html">AI Score</a></li>
              <li><a href="./talentpool_feature.html">TalentPool</a></li>
              <li><a href="./expert_support.html">Expert Support</a></li>
              <li><a href="./workflow_automation.html">Workflow Automation</a></li>
              <li><a href="./canvider_jobcraft.html">JobCraft</a></li>
              <li><a href="./collaborative_candidate_assessment.html">Team Assessment</a></li>
            </ul>
          </div>

          <!-- Company Section -->
          <div class="footer-section">
            <h4>Company</h4>
            <ul class="footer-links">
              <li><a href="./about_us.html">About Us</a></li>
              <li>
                <button onclick="scrollToSection('#contact')">Contact</button>
              </li>
              <li><a href="./ats_signup.html">Get Started</a></li>
            </ul>
          </div>

          <!-- Product Section -->
          <div class="footer-section">
            <h4>Product</h4>
            <ul class="footer-links">
              <li>
                <button onclick="scrollToSection('#features')">Features</button>
              </li>
              <li>
                <button onclick="scrollToSection('#pricing')">Pricing</button>
              </li>
              <li><a href="./ats_signup.html">Start Free Trial</a></li>
            </ul>
          </div>

          <!-- Legal Section -->
          <div class="footer-section">
            <h4>Legal</h4>
            <ul class="footer-links">
              <li><a href="./legal/terms-of-service.pdf" target="_blank">Terms of Service</a></li>
              <li><a href="./legal/privacy-policy.pdf" target="_blank">Privacy Policy</a></li>
              <li><a href="./legal/data-processing-agreement.pdf" target="_blank">Data Processing Agreement</a></li>
              <li><a href="./legal/faq-data-ai-usage.pdf" target="_blank">FAQ – Data & AI Usage</a></li>
              <li><a href="./legal/privacy-policy.pdf" target="_blank"></a></li>
            </ul>
          </div>
        </div>

        <div class="footer-bottom">
          <p>&copy; 2024 Canvider. All rights reserved.</p>
          <div class="footer-legal-links">
            <a href="./legal/faq-data-ai-usage.pdf" target="_blank">How we use AI & process data</a>
          </div>
        </div>
      </div>
    </footer>

    <!-- JavaScript for smooth animations -->
    <script>
      // Universal scroll to section function
      function scrollToSection(href) {
        if (href.startsWith("#")) {
          // Check if we're on index.html (or root)
          const currentPath = window.location.pathname;
          const isIndexPage = currentPath === '/' || currentPath.endsWith('/index.html') || currentPath.endsWith('/');

          if (isIndexPage) {
            // We're on index.html, scroll to the section
            const element = document.getElementById(href.substring(1));
            if (element) {
              element.scrollIntoView({ behavior: "smooth" });
            }
          } else {
            // We're on another page, navigate to index.html with the anchor
            window.location.href = './index.html' + href;
          }
        }
      }

      // Header background on scroll
      let header = null;
      function updateHeader() {
        if (!header) header = document.querySelector("header");

        if (window.scrollY > 100) {
          header.style.background = "rgba(10, 10, 10, 0.98)";
          header.style.backdropFilter = "blur(25px)";
          header.style.boxShadow = "0 4px 30px rgba(102, 126, 234, 0.1)";
        } else {
          header.style.background = "rgba(10, 10, 10, 0.95)";
          header.style.backdropFilter = "blur(20px)";
          header.style.boxShadow = "none";
        }
      }

      // Throttled scroll event
      let ticking = false;
      function requestUpdate() {
        if (!ticking) {
          requestAnimationFrame(() => {
            updateHeader();
            ticking = false;
          });
          ticking = true;
        }
      }

      window.addEventListener("scroll", requestUpdate, { passive: true });

      // Initial setup
      updateHeader();
    </script>
  </body>
</html>
